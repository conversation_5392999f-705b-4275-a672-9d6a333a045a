# GrocEase Backend API

A comprehensive Spring Boot backend for the GrocEase mobile grocery application, featuring JWT authentication, PostgreSQL database, Cloudinary image storage, Firebase push notifications, advanced analytics, and RESTful APIs.

![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2.1-brightgreen)
![Java](https://img.shields.io/badge/Java-17-orange)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-12+-blue)
![Firebase](https://img.shields.io/badge/Firebase-FCM-yellow)
![License](https://img.shields.io/badge/License-MIT-green)

## 🚀 Features

### 🔐 Authentication & Security
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **OTP Verification**: Email and phone verification with time-based OTPs
- **Role-Based Access**: User and Admin role management
- **Password Security**: BCrypt encryption with secure password policies
- **Rate Limiting**: API and notification rate limiting with Bucket4j

### 👥 User Management
- **User Profiles**: Complete user profile management with avatars
- **Address Management**: Multiple delivery addresses with default selection
- **Account Verification**: Email and phone number verification
- **Birthday Tracking**: Birthday notifications with special offers

### 🛒 Product & Catalog Management
- **Product Catalog**: Comprehensive product management with categories
- **Search & Filtering**: Advanced search with category and tag filtering
- **Image Management**: Cloudinary integration for product images
- **Inventory Tracking**: Stock management with low stock alerts
- **Featured Products**: Promotional product highlighting

### 📦 Advanced Order Management
- **Order Lifecycle**: Complete order processing from creation to delivery
- **Status Tracking**: Real-time order status with validation rules
- **Order History**: Detailed order tracking with status change audit trail
- **Admin Controls**: Admin-only order status management
- **Automated Notifications**: Push notifications for order updates

### 📊 Analytics & Business Intelligence
- **Sales Analytics**: Monthly/weekly sales data with revenue metrics
- **Product Performance**: Most sold products and category analytics
- **User Engagement**: User activity, retention, and growth metrics
- **Dashboard Overview**: Real-time business metrics and KPIs
- **Growth Tracking**: Revenue and order growth percentage calculations

### 🔔 Push Notification System
- **Firebase Integration**: Complete FCM setup with device token management
- **Automated Triggers**: Order updates, birthday wishes, promotional offers
- **Notification Templates**: Predefined templates for different event types
- **Delivery Tracking**: Complete notification history and status tracking
- **Rate Limiting**: Smart rate limiting to prevent notification spam

### 🎯 Automated Marketing
- **Birthday Campaigns**: Automated birthday wishes with discount codes
- **Promotional Broadcasts**: Weekly promotional offers and new product alerts
- **Cart Abandonment**: Automated cart reminder notifications
- **Low Stock Alerts**: Notifications for wishlisted items running low

### 🛠️ Technical Features
- **Image Upload**: Cloudinary integration for all image types
- **Email Service**: SMTP integration for OTP and notifications
- **Scheduled Tasks**: Automated daily/weekly notification triggers
- **Error Handling**: Comprehensive error handling with meaningful messages
- **Logging**: Detailed logging for debugging and monitoring
- **Database Auditing**: Automatic created/updated timestamps with versioning

## 🛠️ Tech Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Framework** | Spring Boot | 3.2.1 | Main application framework |
| **Language** | Java | 17 | Programming language |
| **Database** | PostgreSQL | 12+ | Primary data storage |
| **ORM** | Spring Data JPA / Hibernate | - | Database abstraction layer |
| **Security** | Spring Security + JWT | - | Authentication & authorization |
| **Push Notifications** | Firebase Cloud Messaging | - | Mobile push notifications |
| **Image Storage** | Cloudinary | - | Cloud image storage & CDN |
| **Email** | Spring Mail | - | Email notifications |
| **Rate Limiting** | Bucket4j | 7.6.0 | API rate limiting |
| **Scheduling** | Spring Scheduler | - | Automated tasks |
| **Build Tool** | Maven | 3.6+ | Dependency management |
| **Code Reduction** | Lombok | - | Boilerplate code reduction |
| **Mapping** | ModelMapper | 3.2.0 | DTO mapping |

## 📋 Prerequisites

Before setting up the project, ensure you have:

- ☑️ **Java 17 or higher** - [Download here](https://adoptium.net/)
- ☑️ **PostgreSQL 12 or higher** - [Download here](https://www.postgresql.org/download/)
- ☑️ **Maven 3.6 or higher** - [Download here](https://maven.apache.org/download.cgi)
- ☑️ **Cloudinary account** - [Sign up here](https://cloudinary.com/) (for image uploads)
- ☑️ **SMTP server** - Gmail SMTP recommended (for email notifications)
- ☑️ **Firebase project** - [Create here](https://console.firebase.google.com/) (for push notifications)

## 🚀 Quick Start

### 1. 🗄️ Database Setup

Create a PostgreSQL database:

```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE grocease_db;
CREATE USER grocease_user WITH PASSWORD 'grocease_password';
GRANT ALL PRIVILEGES ON DATABASE grocease_db TO grocease_user;

-- Grant additional permissions for schema operations
GRANT CREATE ON DATABASE grocease_db TO grocease_user;
ALTER USER grocease_user CREATEDB;
```

### 2. ⚙️ Environment Configuration

Create a `.env` file or set environment variables:

```bash
# Database Configuration
DB_USERNAME=grocease_user
DB_PASSWORD=grocease_password

# JWT Security
JWT_SECRET=your-super-secret-jwt-key-make-it-at-least-32-characters-long

# Cloudinary Configuration (Required for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email Configuration (Required for OTP)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Firebase Configuration (Optional - for push notifications)
FIREBASE_CONFIG_FILE=firebase-service-account.json

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:19006,http://localhost:8081
```

### 3. 🔥 Firebase Setup (Optional)

For push notifications, follow the [Firebase Setup Guide](FIREBASE_SETUP.md):

1. Create a Firebase project
2. Generate service account key
3. Download `firebase-service-account.json`
4. Place it in `src/main/resources/`

### 4. 🏗️ Build and Run

```bash
# Navigate to backend directory
cd backend

# Install dependencies and build
mvn clean install

# Run the application
mvn spring-boot:run

# Or run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 5. ✅ Verify Installation

The API will be available at `http://localhost:8080/api`

Test the health endpoint:
```bash
curl http://localhost:8080/api/actuator/health
```

Expected response:
```json
{"status":"UP"}
```

## 📚 API Documentation

### 🔐 Authentication Endpoints
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/api/auth/register` | User registration with email verification | ❌ |
| `POST` | `/api/auth/login` | User login with JWT token response | ❌ |
| `POST` | `/api/auth/forgot-password` | Request password reset OTP | ❌ |
| `POST` | `/api/auth/reset-password` | Reset password with OTP token | ❌ |
| `POST` | `/api/auth/verify-otp` | Verify OTP for email/phone | ❌ |
| `POST` | `/api/auth/resend-otp` | Resend OTP token | ❌ |

### 👤 User Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/users/profile` | Get current user profile | ✅ User |
| `PUT` | `/api/users/profile` | Update user profile | ✅ User |
| `GET` | `/api/users/addresses` | Get user delivery addresses | ✅ User |
| `POST` | `/api/users/addresses` | Create new delivery address | ✅ User |
| `PUT` | `/api/users/addresses/{id}` | Update existing address | ✅ User |
| `DELETE` | `/api/users/addresses/{id}` | Delete address | ✅ User |

### 🛒 Product Catalog
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/products` | Get products with pagination, search, filtering | ❌ |
| `GET` | `/api/products/{id}` | Get product details by ID | ❌ |
| `GET` | `/api/products/featured` | Get featured products | ❌ |
| `GET` | `/api/products/search?query={term}` | Search products by name/description/tags | ❌ |
| `GET` | `/api/categories` | Get all active categories | ❌ |
| `GET` | `/api/categories/{id}` | Get category details by ID | ❌ |

### 📦 Order Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/api/orders` | Create new order | ✅ User |
| `GET` | `/api/orders` | Get user orders with pagination | ✅ User |
| `GET` | `/api/orders/{id}` | Get order details by ID | ✅ User |
| `GET` | `/api/orders/number/{orderNumber}` | Get order by order number | ✅ User |

### 🎯 Marketing & Content
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/banners` | Get all active promotional banners | ❌ |

### 📤 File Upload
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/api/upload/avatar` | Upload user avatar image | ✅ User |
| `POST` | `/api/upload/product` | Upload product image | ✅ Admin |
| `POST` | `/api/upload/category` | Upload category image | ✅ Admin |
| `POST` | `/api/upload/banner` | Upload banner image | ✅ Admin |

### 📊 Analytics (Admin Only)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/analytics/sales/monthly?months=12` | Monthly sales data and trends | ✅ Admin |
| `GET` | `/api/analytics/sales/weekly?weeks=12` | Weekly sales data and trends | ✅ Admin |
| `GET` | `/api/analytics/products/most-sold?days=30&limit=10` | Top selling products | ✅ Admin |
| `GET` | `/api/analytics/categories/popular?days=30` | Popular categories by sales | ✅ Admin |
| `GET` | `/api/analytics/users/engagement?days=30` | User engagement metrics | ✅ Admin |

### 🎛️ Admin Dashboard (Admin Only)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/admin/dashboard/overview` | Real-time dashboard metrics | ✅ Admin |
| `PUT` | `/api/admin/orders/{id}/status` | Update order status with validation | ✅ Admin |
| `POST` | `/api/admin/notifications/send` | Send push notifications | ✅ Admin |
| `GET` | `/api/admin/notifications/history` | Notification delivery history | ✅ Admin |

### 🔔 Push Notifications
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/api/notifications/register-token` | Register FCM device token | ✅ User |
| `POST` | `/api/notifications/unregister-token` | Unregister device token | ✅ User |
| `GET` | `/api/notifications/history` | User notification history | ✅ User |

## 🔑 Authentication

The API uses **JWT (JSON Web Tokens)** for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### Getting Started with Authentication

1. **Register a new user:**
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "password": "securePassword123",
    "confirmPassword": "securePassword123"
  }'
```

2. **Login to get JWT token:**
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securePassword123"
  }'
```

3. **Use the token in subsequent requests:**
```bash
curl -X GET http://localhost:8080/api/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📋 Request/Response Format

### ✅ Success Response
```json
{
  "data": {
    "id": "1",
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "success": true,
  "message": "Operation successful"
}
```

### ❌ Error Response
```json
{
  "success": false,
  "error": "Validation failed",
  "message": "Email is required",
  "data": {
    "email": "Email is required",
    "password": "Password must be at least 6 characters"
  }
}
```

### 📄 Paginated Response
```json
{
  "data": [
    {"id": "1", "name": "Product 1"},
    {"id": "2", "name": "Product 2"}
  ],
  "pagination": {
    "page": 0,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### 🚦 HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| `200` | Success |
| `201` | Created |
| `400` | Bad Request (validation errors) |
| `401` | Unauthorized (invalid/missing token) |
| `403` | Forbidden (insufficient permissions) |
| `404` | Not Found |
| `429` | Too Many Requests (rate limited) |
| `500` | Internal Server Error |

## 🗄️ Database Schema

The application uses a comprehensive database schema with the following entities:

### 👤 User Management
- **User**: User accounts with authentication, profile info, and birthdate
- **Address**: Multiple delivery addresses per user with default selection
- **OtpToken**: Time-based OTP tokens for email/phone verification

### 🛒 Product Catalog
- **Category**: Product categories with images and sorting
- **Product**: Complete product catalog with pricing, inventory, and ratings
- **ProductTag**: Searchable tags for products

### 📦 Order Management
- **Order**: Customer orders with status tracking and delivery info
- **OrderItem**: Individual items within orders with pricing
- **OrderStatusHistory**: Complete audit trail of order status changes

### 🔔 Notification System
- **UserDeviceToken**: FCM device tokens for push notifications
- **NotificationHistory**: Complete notification delivery tracking

### 🎯 Marketing
- **Banner**: Promotional banners for the home screen

### 🔗 Entity Relationships

```mermaid
erDiagram
    User ||--o{ Address : has
    User ||--o{ Order : places
    User ||--o{ UserDeviceToken : owns
    User ||--o{ NotificationHistory : receives
    User ||--o{ OtpToken : generates

    Category ||--o{ Product : contains
    Product ||--o{ ProductTag : has
    Product ||--o{ OrderItem : ordered_as

    Order ||--o{ OrderItem : contains
    Order ||--o{ OrderStatusHistory : tracks
    Order }o--|| Address : delivered_to
```

## 🛠️ Development

### 🧪 Running Tests
```bash
# Run all tests
mvn test

# Run tests with coverage
mvn test jacoco:report

# Run specific test class
mvn test -Dtest=UserServiceTest

# Run tests in specific package
mvn test -Dtest="com.grocease.service.*"
```

### 📝 Code Style & Standards
- **Lombok**: Reduces boilerplate code - ensure your IDE has the Lombok plugin installed
- **Java Code Conventions**: Follow standard Java naming conventions
- **REST API Standards**: RESTful endpoint design with proper HTTP methods
- **Error Handling**: Comprehensive error handling with meaningful messages
- **Documentation**: Javadoc for public methods and complex logic

### 📊 Logging & Monitoring

**Log Levels:**
- `DEBUG`: Detailed application flow (development only)
- `INFO`: General application events
- `WARN`: Potentially harmful situations
- `ERROR`: Error events that allow application to continue

**Log Locations:**
- Console output (development)
- `logs/grocease-backend.log` (production)

**Key Log Categories:**
```bash
# Authentication events
tail -f logs/grocease-backend.log | grep -i "auth"

# Order processing
tail -f logs/grocease-backend.log | grep -i "order"

# Notification delivery
tail -f logs/grocease-backend.log | grep -i "notification"

# Analytics queries
tail -f logs/grocease-backend.log | grep -i "analytics"
```

### 🔧 Development Tools

**Recommended IDE Setup:**
- IntelliJ IDEA or Eclipse
- Lombok plugin
- Spring Boot plugin
- Database client (DBeaver, pgAdmin)

**Useful Maven Commands:**
```bash
# Clean and compile
mvn clean compile

# Run with dev profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Generate dependency tree
mvn dependency:tree

# Check for updates
mvn versions:display-dependency-updates
```

## 🚀 Production Deployment

### 📋 Pre-Deployment Checklist

- [ ] **Environment Variables**: All required environment variables configured
- [ ] **Database**: Production PostgreSQL instance set up and accessible
- [ ] **SSL/TLS**: HTTPS certificates configured
- [ ] **Reverse Proxy**: Nginx/Apache configured for load balancing
- [ ] **Monitoring**: Application monitoring and alerting set up
- [ ] **Backup Strategy**: Database backup and recovery procedures
- [ ] **Security**: Security headers and firewall rules configured
- [ ] **Performance**: Load testing completed

### 🐳 Docker Deployment

```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app
COPY target/grocease-backend-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=grocease_db
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### ☁️ Cloud Deployment Options

**AWS:**
- **Elastic Beanstalk**: Easy deployment with auto-scaling
- **ECS/Fargate**: Containerized deployment
- **RDS**: Managed PostgreSQL database
- **S3**: Static file storage (alternative to Cloudinary)

**Google Cloud:**
- **App Engine**: Serverless deployment
- **Cloud Run**: Containerized serverless
- **Cloud SQL**: Managed PostgreSQL
- **Cloud Storage**: File storage

**Azure:**
- **App Service**: Web app hosting
- **Container Instances**: Container deployment
- **Database for PostgreSQL**: Managed database
- **Blob Storage**: File storage

### 🔒 Security Considerations

1. **Environment Variables**: Never commit secrets to version control
2. **Database Security**: Use connection pooling and encrypted connections
3. **API Security**: Implement rate limiting and input validation
4. **HTTPS**: Always use HTTPS in production
5. **Monitoring**: Set up security monitoring and alerting
6. **Updates**: Keep dependencies updated for security patches

### 📊 Monitoring & Observability

**Application Metrics:**
- Response times and throughput
- Error rates and exceptions
- Database connection pool metrics
- JVM memory and garbage collection

**Business Metrics:**
- Order completion rates
- User registration and activity
- Notification delivery rates
- Revenue and sales metrics

**Recommended Tools:**
- **Prometheus + Grafana**: Metrics and dashboards
- **ELK Stack**: Centralized logging
- **New Relic/DataDog**: APM monitoring
- **Sentry**: Error tracking

## 🤝 Contributing

We welcome contributions to the GrocEase backend! Here's how you can help:

### 🔄 Development Workflow

1. **Fork the repository**
   ```bash
   git clone https://github.com/your-username/grocease-backend.git
   cd grocease-backend
   ```

2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes**
   - Follow the existing code style and conventions
   - Add comprehensive tests for new features
   - Update documentation as needed

4. **Test your changes**
   ```bash
   mvn test
   mvn spring-boot:run # Test locally
   ```

5. **Submit a pull request**
   - Provide a clear description of changes
   - Reference any related issues
   - Ensure all tests pass

### 📝 Contribution Guidelines

- **Code Style**: Follow Java conventions and use Lombok appropriately
- **Testing**: Maintain test coverage above 80%
- **Documentation**: Update README and API docs for new features
- **Security**: Never commit sensitive information
- **Performance**: Consider performance impact of changes

### 🐛 Reporting Issues

When reporting bugs, please include:
- Steps to reproduce the issue
- Expected vs actual behavior
- Environment details (Java version, OS, etc.)
- Relevant log output

### 💡 Feature Requests

For new features, please:
- Check existing issues first
- Provide detailed use case description
- Consider backward compatibility
- Discuss implementation approach

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

```
MIT License

Copyright (c) 2024 GrocEase

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🙏 Acknowledgments

- **Spring Boot Team** - For the excellent framework
- **Firebase Team** - For reliable push notification service
- **Cloudinary** - For seamless image management
- **PostgreSQL Community** - For the robust database system
- **Open Source Community** - For the amazing libraries and tools

## 📞 Support

- **Documentation**: [Firebase Setup Guide](FIREBASE_SETUP.md)
- **Issues**: [GitHub Issues](https://github.com/your-username/grocease-backend/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/grocease-backend/discussions)

---

**Built with ❤️ for the grocery delivery community**
