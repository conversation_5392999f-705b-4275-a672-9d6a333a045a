# GrocEase Backend API

A comprehensive Spring Boot backend for the GrocEase mobile grocery application, featuring JWT authentication, PostgreSQL database, Cloudinary image storage, and RESTful APIs.

## Features

- **Authentication & Authorization**: JWT-based authentication with OTP verification
- **User Management**: User profiles, addresses, and account management
- **Product Catalog**: Categories, products with search and filtering
- **Order Management**: Complete order lifecycle with status tracking and validation
- **Analytics & Dashboard**: Comprehensive sales analytics and admin dashboard
- **Push Notifications**: Firebase Cloud Messaging integration with automated triggers
- **Image Upload**: Cloudinary integration for image storage
- **Email Service**: OTP and notification emails
- **Rate Limiting**: API and notification rate limiting with Bucket4j
- **Database**: PostgreSQL with JPA/Hibernate
- **Security**: Spring Security with password encryption
- **Validation**: Request validation with custom error handling
- **Scheduling**: Automated tasks for notifications and analytics

## Tech Stack

- **Framework**: Spring Boot 3.2.1
- **Database**: PostgreSQL
- **ORM**: Spring Data JPA / Hibernate
- **Security**: Spring Security + JWT
- **Push Notifications**: Firebase Cloud Messaging
- **Image Storage**: Cloudinary
- **Email**: Spring Mail
- **Rate Limiting**: Bucket4j
- **Scheduling**: Spring Scheduler
- **Build Tool**: Maven
- **Java Version**: 17

## Prerequisites

- Java 17 or higher
- PostgreSQL 12 or higher
- Maven 3.6 or higher
- Cloudinary account (for image uploads)
- SMTP server (for email notifications)
- Firebase project (for push notifications)

## Setup Instructions

### 1. Database Setup

Create a PostgreSQL database:

```sql
CREATE DATABASE grocease_db;
CREATE USER grocease_user WITH PASSWORD 'grocease_password';
GRANT ALL PRIVILEGES ON DATABASE grocease_db TO grocease_user;
```

### 2. Environment Variables

Set the following environment variables or update `application.yml`:

```bash
# Database
DB_USERNAME=grocease_user
DB_PASSWORD=grocease_password

# JWT
JWT_SECRET=your-secret-key-here-make-it-long-and-secure

# Cloudinary
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email
MAIL_HOST=smtp.gmail.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Firebase (optional - for push notifications)
FIREBASE_CONFIG_FILE=firebase-service-account.json

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:19006
```

### 3. Build and Run

```bash
# Clone the repository
cd backend

# Build the project
mvn clean install

# Run the application
mvn spring-boot:run
```

The API will be available at `http://localhost:8080/api`

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with OTP
- `POST /api/auth/verify-otp` - Verify OTP
- `POST /api/auth/resend-otp` - Resend OTP

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/addresses` - Get user addresses
- `POST /api/users/addresses` - Create new address
- `PUT /api/users/addresses/{id}` - Update address
- `DELETE /api/users/addresses/{id}` - Delete address

### Products
- `GET /api/products` - Get products (with pagination, search, filtering)
- `GET /api/products/{id}` - Get product by ID
- `GET /api/products/featured` - Get featured products
- `GET /api/products/search` - Search products

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/{id}` - Get category by ID

### Orders
- `POST /api/orders` - Create new order
- `GET /api/orders` - Get user orders (with pagination)
- `GET /api/orders/{id}` - Get order by ID
- `GET /api/orders/number/{orderNumber}` - Get order by number

### Banners
- `GET /api/banners` - Get all active banners

### File Upload
- `POST /api/upload/avatar` - Upload user avatar
- `POST /api/upload/product` - Upload product image
- `POST /api/upload/category` - Upload category image
- `POST /api/upload/banner` - Upload banner image

### Analytics (Admin Only)
- `GET /api/analytics/sales/monthly` - Get monthly sales data
- `GET /api/analytics/sales/weekly` - Get weekly sales data
- `GET /api/analytics/products/most-sold` - Get most sold products
- `GET /api/analytics/categories/popular` - Get popular categories
- `GET /api/analytics/users/engagement` - Get user engagement metrics

### Admin Dashboard (Admin Only)
- `GET /api/admin/dashboard/overview` - Get dashboard overview
- `PUT /api/admin/orders/{id}/status` - Update order status
- `POST /api/admin/notifications/send` - Send notifications
- `GET /api/admin/notifications/history` - Get notification history

### Notifications
- `POST /api/notifications/register-token` - Register device token for push notifications
- `POST /api/notifications/unregister-token` - Unregister device token
- `GET /api/notifications/history` - Get user notification history

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Request/Response Format

### Success Response
```json
{
  "data": { ... },
  "success": true,
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error type",
  "message": "Error description"
}
```

### Paginated Response
```json
{
  "data": [...],
  "pagination": {
    "page": 0,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

## Database Schema

The application uses the following main entities:
- **User**: User accounts with authentication and birthdate
- **Address**: User delivery addresses
- **Category**: Product categories
- **Product**: Product catalog
- **ProductTag**: Product tags for search
- **Order**: Customer orders
- **OrderItem**: Individual order items
- **OrderStatusHistory**: Order status change tracking
- **Banner**: Promotional banners
- **OtpToken**: OTP tokens for verification
- **UserDeviceToken**: Device tokens for push notifications
- **NotificationHistory**: Notification delivery tracking

## Development

### Running Tests
```bash
mvn test
```

### Code Style
The project uses Lombok to reduce boilerplate code. Make sure your IDE has Lombok plugin installed.

### Logging
Logs are written to `logs/grocease-backend.log` and console. Log levels can be configured in `application.yml`.

## Production Deployment

1. Set all environment variables
2. Configure production database
3. Set up SSL/TLS
4. Configure reverse proxy (nginx/Apache)
5. Set up monitoring and logging
6. Configure backup strategy

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
