package com.grocease.service;

import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.order.CreateOrderRequest;
import com.grocease.dto.order.OrderDto;
import com.grocease.entity.*;
import com.grocease.exception.BadRequestException;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.AddressRepository;
import com.grocease.repository.OrderRepository;
import com.grocease.repository.ProductRepository;
import com.grocease.repository.UserRepository;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrderService {

    private final OrderRepository orderRepository;
    private final UserRepository userRepository;
    private final ProductRepository productRepository;
    private final AddressRepository addressRepository;
    private final DtoMapper dtoMapper;

    @Transactional
    public OrderDto createOrder(Long userId, CreateOrderRequest request) {
        // Validate user
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Validate delivery address
        Address deliveryAddress = addressRepository.findById(request.getDeliveryAddressId())
                .orElseThrow(() -> new ResourceNotFoundException("Address not found with id: " + request.getDeliveryAddressId()));

        if (!deliveryAddress.getUser().getId().equals(userId)) {
            throw new BadRequestException("Address does not belong to user");
        }

        // Create order
        Order order = Order.builder()
                .orderNumber(generateOrderNumber())
                .total(request.getTotal())
                .subtotal(request.getSubtotal())
                .deliveryFee(request.getDeliveryFee())
                .discount(request.getDiscount())
                .status(Order.OrderStatus.PENDING)
                .estimatedDelivery(LocalDateTime.now().plusHours(2)) // 2 hours from now
                .user(user)
                .deliveryAddress(deliveryAddress)
                .items(new ArrayList<>())
                .build();

        // Create order items
        BigDecimal calculatedSubtotal = BigDecimal.ZERO;
        for (CreateOrderRequest.OrderItemRequest itemRequest : request.getItems()) {
            Product product = productRepository.findById(itemRequest.getProductId())
                    .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + itemRequest.getProductId()));

            if (!product.getInStock()) {
                throw new BadRequestException("Product is out of stock: " + product.getName());
            }

            if (product.getStockQuantity() < itemRequest.getQuantity()) {
                throw new BadRequestException("Insufficient stock for product: " + product.getName());
            }

            BigDecimal itemTotal = product.getPrice().multiply(BigDecimal.valueOf(itemRequest.getQuantity()));
            calculatedSubtotal = calculatedSubtotal.add(itemTotal);

            OrderItem orderItem = OrderItem.builder()
                    .quantity(itemRequest.getQuantity())
                    .unitPrice(product.getPrice())
                    .totalPrice(itemTotal)
                    .order(order)
                    .product(product)
                    .build();

            order.getItems().add(orderItem);

            // Update product stock
            product.setStockQuantity(product.getStockQuantity() - itemRequest.getQuantity());
            productRepository.save(product);
        }

        // Validate calculated totals
        if (calculatedSubtotal.compareTo(request.getSubtotal()) != 0) {
            throw new BadRequestException("Subtotal mismatch. Expected: " + calculatedSubtotal + ", Received: " + request.getSubtotal());
        }

        BigDecimal calculatedTotal = calculatedSubtotal.add(request.getDeliveryFee()).subtract(request.getDiscount());
        if (calculatedTotal.compareTo(request.getTotal()) != 0) {
            throw new BadRequestException("Total mismatch. Expected: " + calculatedTotal + ", Received: " + request.getTotal());
        }

        // Save order
        Order savedOrder = orderRepository.save(order);
        
        // Update order status to confirmed
        savedOrder.setStatus(Order.OrderStatus.CONFIRMED);
        savedOrder = orderRepository.save(savedOrder);

        log.info("Order created successfully: {} for user: {}", savedOrder.getOrderNumber(), userId);

        return dtoMapper.toOrderDto(savedOrder);
    }

    public PaginatedResponse<OrderDto> getUserOrders(Long userId, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Order> orderPage = orderRepository.findByUserIdWithDetails(userId, pageable);

        List<OrderDto> orderDtos = dtoMapper.toOrderDtoList(orderPage.getContent());

        return PaginatedResponse.<OrderDto>builder()
                .data(orderDtos)
                .pagination(PaginatedResponse.PaginationInfo.builder()
                        .page(page)
                        .limit(limit)
                        .total(orderPage.getTotalElements())
                        .totalPages(orderPage.getTotalPages())
                        .build())
                .build();
    }

    public OrderDto getOrderById(Long userId, Long orderId) {
        Order order = orderRepository.findByIdWithDetails(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found with id: " + orderId));

        if (!order.getUser().getId().equals(userId)) {
            throw new BadRequestException("Order does not belong to user");
        }

        return dtoMapper.toOrderDto(order);
    }

    public OrderDto getOrderByNumber(Long userId, String orderNumber) {
        Order order = orderRepository.findByOrderNumberWithDetails(orderNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found with number: " + orderNumber));

        if (!order.getUser().getId().equals(userId)) {
            throw new BadRequestException("Order does not belong to user");
        }

        return dtoMapper.toOrderDto(order);
    }

    @Transactional
    public OrderDto updateOrderStatus(Long orderId, Order.OrderStatus status) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found with id: " + orderId));

        order.setStatus(status);
        
        if (status == Order.OrderStatus.DELIVERED) {
            order.setDeliveredAt(LocalDateTime.now());
        }

        Order savedOrder = orderRepository.save(order);
        return dtoMapper.toOrderDto(savedOrder);
    }

    private String generateOrderNumber() {
        return "ORD-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
